// إضافة هذه القواعد إلى firestore.rules لدعم المراجعة

// قواعد reviewStats - للسماح بالقراءة والكتابة لإحصائيات المراجعة
match /users/{userId}/reviewStats/{document} {
  allow read, write: if request.auth != null && request.auth.uid == userId;
}

// أو إذا كانت القواعد موجودة بالفعل، تأكد من أنها تشمل reviewStats:
match /users/{userId}/{document=**} {
  allow read, write: if request.auth != null && request.auth.uid == userId;
}
