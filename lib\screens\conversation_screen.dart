import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/points.dart';
import '../models/conversation_model.dart';
import '../models/message_model.dart';
import '../providers/level_provider.dart';
import '../providers/points_provider.dart';
import '../services/conversation_service.dart';
import '../services/points_animation_service.dart';
import '../services/read_conversation_service.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class ConversationScreen extends StatefulWidget {
  static const routeName = '/conversation';

  final int? levelId;
  final int? cycleId;
  final int? groupId;
  final String? title;

  const ConversationScreen({
    Key? key,
    this.levelId,
    this.cycleId,
    this.groupId,
    this.title,
  }) : super(key: key);

  @override
  State<ConversationScreen> createState() => _ConversationScreenState();
}

class _ConversationScreenState extends State<ConversationScreen> {
  bool _isLoading = false;
  int _levelId = 0;
  int _cycleId = 0;
  int _groupId = 0;
  String _title = '';
  int _completedSentences = 0;
  double _accuracy = 0.0;
  int _totalSentences = 0;
  ConversationModel? _currentConversation;
  List<MessageModel> _messages = [];
  List<Map<String, dynamic>> _conversationData = [];
  int _currentSpeakerIndex = 0;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Primero intentamos obtener los parámetros del widget
    if (widget.levelId != null &&
        widget.cycleId != null &&
        widget.groupId != null &&
        widget.title != null) {
      _levelId = widget.levelId!;
      _cycleId = widget.cycleId!;
      _groupId = widget.groupId!;
      _title = widget.title!;
      _loadConversation();
      return;
    }

    // Si no hay parámetros en el widget, intentamos obtenerlos de los argumentos de la ruta
    final args =
        ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    if (args != null) {
      _levelId = args['levelId'] as int;
      _cycleId = args['cycleId'] as int? ?? 0;
      _groupId = args['groupId'] as int;
      _title = args['title'] as String;
      _loadConversation();
    }
  }

  @override
  void initState() {
    super.initState();
  }

  Future<void> _loadConversation() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // الحصول على معلومات المجموعة من مزود المستويات
      final levelProvider = Provider.of<LevelProvider>(context, listen: false);
      final level = levelProvider.getLevelById(_levelId);

      if (level != null) {
        // البحث عن الدورة
        final cycle = level.cycles.firstWhere(
          (cycle) => cycle.id == _cycleId,
          orElse: () => throw Exception('الدورة غير موجودة'),
        );

        // البحث عن المجموعة داخل الدورة
        final lessonGroup = cycle.lessonGroups.firstWhere(
          (group) => group.id == _groupId,
          orElse: () => throw Exception('مجموعة الدروس غير موجودة'),
        );

        _completedSentences = lessonGroup.completedSentences;
        _accuracy = lessonGroup.accuracy;
        _totalSentences = lessonGroup.totalSentences;
        _currentSpeakerIndex =
            lessonGroup.completedSentences; // المؤشر = عدد الجمل المكتملة
      }

      // تحميل بيانات المحادثة من خدمة المحادثة
      final conversationService =
          Provider.of<ConversationService>(context, listen: false);

      // تحميل المحادثات حسب المستوى
      await for (List<ConversationModel> conversations
          in conversationService.getConversationsByLevel('$_levelId')) {
        if (conversations.isNotEmpty) {
          setState(() {
            _currentConversation = conversations.first;
            _messages = _currentConversation!.messages;
            _conversationData = _convertToConversationData(_messages);
          });
          break;
        }
      }

      // إذا لم يتم العثور على محادثات، استخدم البيانات الوهمية
      if (_currentConversation == null) {
        _conversationData = _getMockConversationData();
      }

      // تحميل حالة المحادثة المحفوظة
      await _loadConversationState();
    } catch (e) {
      debugPrint('خطأ في تحميل المحادثة: $e');
      // استخدم البيانات الوهمية في حالة حدوث خطأ
      _conversationData = _getMockConversationData();
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// تحميل حالة المحادثة المحفوظة
  Future<void> _loadConversationState() async {
    try {
      if (_groupId == 0 || _currentConversation == null) {
        debugPrint(
            'لا يمكن تحميل حالة المحادثة - معرف المجموعة أو المحادثة غير متاح');
        return;
      }

      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        debugPrint('لا يوجد مستخدم مسجل');
        return;
      }

      debugPrint('تحميل حالة المحادثة للمجموعة: $_groupId');

      // تحميل من readConversations في Firebase
      final readConversationsDoc = await FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .collection('readConversations')
          .doc(_groupId.toString())
          .get();

      if (readConversationsDoc.exists) {
        final data = readConversationsDoc.data();
        if (data != null && data['sentences'] != null) {
          final sentences = data['sentences'] as List<dynamic>;

          // تحديد عدد الرسائل المعروضة بناءً على الجمل المحفوظة
          final readSentenceIds =
              sentences.map((s) => s['sentenceId'] as String).toSet();

          // حساب عدد الرسائل المقروءة
          int readMessagesCount = 0;
          for (int i = 0; i < _messages.length; i++) {
            if (readSentenceIds.contains(_messages[i].id)) {
              readMessagesCount = i + 1; // +1 لأن الفهرس يبدأ من 0
            } else {
              break; // إذا وجدنا رسالة غير مقروءة، نتوقف
            }
          }

          if (readMessagesCount > 0) {
            setState(() {
              _currentSpeakerIndex = readMessagesCount;
            });
            debugPrint(
                'تم استعادة حالة المحادثة: $_currentSpeakerIndex رسائل معروضة');
          }
        }
      } else {
        debugPrint('لا توجد حالة محفوظة للمحادثة');
      }
    } catch (e) {
      debugPrint('خطأ في تحميل حالة المحادثة: $e');
    }
  }

  // تحويل رسائل المحادثة إلى تنسيق عرض المحادثة
  List<Map<String, dynamic>> _convertToConversationData(
      List<MessageModel> messages) {
    List<Map<String, dynamic>> conversationData = [];

    for (int i = 0; i < messages.length; i++) {
      final message = messages[i];
      // التحقق مما إذا كانت الرسالة مقروءة بالفعل من قبل المستخدم الحالي
      final isRead =
          message.isReadByUser(_currentConversation?.createdBy ?? '');

      conversationData.add({
        'speaker': message.isPersonA ? 'Sarah' : 'John',
        'avatar': message.isPersonA
            ? 'assets/avatars/avataaars_1.png'
            : 'assets/avatars/avataaars_2.png',
        'text': message.englishText,
        'translation': message.arabicText,
        'isCompleted': isRead,
        'messageIndex': i, // إضافة مؤشر الرسالة للاستخدام في تحديث التقدم
      });
    }

    return conversationData;
  }

  // بيانات محادثة وهمية للعرض
  List<Map<String, dynamic>> _getMockConversationData() {
    return [
      {
        'speaker': 'Sarah',
        'avatar': 'assets/avatars/avataaars_1.png',
        'text': 'Hello! How are you today?',
        'translation': 'مرحبا! كيف حالك اليوم؟',
        'isCompleted': false,
      },
      {
        'speaker': 'John',
        'avatar': 'assets/avatars/avataaars_2.png',
        'text': 'I\'m doing great, thank you! How about you?',
        'translation': 'أنا بخير، شكرا لك! ماذا عنك؟',
        'isCompleted': false,
      },
      {
        'speaker': 'Sarah',
        'avatar': 'assets/avatars/avataaars_1.png',
        'text': 'I\'m fine too. What are your plans for today?',
        'translation': 'أنا بخير أيضا. ما هي خططك لهذا اليوم؟',
        'isCompleted': false,
      },
      {
        'speaker': 'John',
        'avatar': 'assets/avatars/avataaars_2.png',
        'text':
            'I\'m going to the library to study. Would you like to join me?',
        'translation': 'سأذهب إلى المكتبة للدراسة. هل ترغب في الانضمام إلي؟',
        'isCompleted': false,
      },
      {
        'speaker': 'Sarah',
        'avatar': 'assets/avatars/avataaars_1.png',
        'text': 'That sounds great! What time are you planning to go?',
        'translation': 'هذا يبدو رائعا! في أي وقت تخطط للذهاب؟',
        'isCompleted': false,
      },
      {
        'speaker': 'John',
        'avatar': 'assets/avatars/avataaars_2.png',
        'text': 'I\'m thinking around 2 PM. Does that work for you?',
        'translation': 'أفكر في حوالي الساعة 2 ظهرا. هل هذا مناسب لك؟',
        'isCompleted': false,
      },
      {
        'speaker': 'Sarah',
        'avatar': 'assets/avatars/avataaars_1.png',
        'text': 'Yes, that works perfectly. Shall we meet at the entrance?',
        'translation': 'نعم، هذا مناسب تماما. هل نلتقي عند المدخل؟',
        'isCompleted': false,
      },
      {
        'speaker': 'John',
        'avatar': 'assets/avatars/avataaars_2.png',
        'text': 'Sounds good! I\'ll see you there at 2 PM.',
        'translation': 'يبدو جيدا! سأراك هناك في الساعة 2 ظهرا.',
        'isCompleted': false,
      },
      {
        'speaker': 'Sarah',
        'avatar': 'assets/avatars/avataaars_1.png',
        'text': 'Great! Don\'t forget to bring your notes.',
        'translation': 'رائع! لا تنس إحضار ملاحظاتك.',
        'isCompleted': false,
      },
      {
        'speaker': 'John',
        'avatar': 'assets/avatars/avataaars_2.png',
        'text': 'I won\'t forget. See you later!',
        'translation': 'لن أنسى. أراك لاحقا!',
        'isCompleted': false,
      },
    ];
  }

  // بناء معلومات التقدم
  Widget _buildProgressInfo() {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'المستوى $_levelId - $_title',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                Text(
                  '$_completedSentences/$_totalSentences',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: Colors.blue,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            LinearProgressIndicator(
              value: _totalSentences > 0
                  ? _completedSentences / _totalSentences
                  : 0,
              backgroundColor: Colors.grey.shade200,
              valueColor: AlwaysStoppedAnimation<Color>(
                _completedSentences == _totalSentences
                    ? Colors.green
                    : Colors.blue,
              ),
              minHeight: 8,
              borderRadius: BorderRadius.circular(4),
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('دقة النطق:'),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getAccuracyColor().withAlpha(25),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${(_accuracy * 100).toInt()}%',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: _getAccuracyColor(),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // بناء فقاعة محادثة
  Widget _buildChatBubble(Map<String, dynamic> message, bool isCurrentSpeaker) {
    final bool isCompleted = message['isCompleted'] as bool;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment:
            isCurrentSpeaker ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isCurrentSpeaker) ...[
            // صورة المتحدث
            Column(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundImage: AssetImage(message['avatar'] as String),
                ),
                const SizedBox(height: 4),
                Text(
                  message['speaker'] as String,
                  style: const TextStyle(fontSize: 12),
                ),
              ],
            ),
            const SizedBox(width: 8),
          ],

          // فقاعة المحادثة
          Flexible(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isCurrentSpeaker
                    ? (isCompleted
                        ? Colors.green.withAlpha(50)
                        : Colors.blue.withAlpha(50))
                    : Colors.grey.shade200,
                borderRadius: BorderRadius.circular(16),
                border: isCompleted
                    ? Border.all(color: Colors.green, width: 1)
                    : null,
              ),
              child: Column(
                crossAxisAlignment: isCurrentSpeaker
                    ? CrossAxisAlignment.end
                    : CrossAxisAlignment.start,
                children: [
                  // النص الإنجليزي
                  Text(
                    message['text'] as String,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.left,
                    textDirection: TextDirection.ltr,
                  ),
                  const SizedBox(height: 8),
                  // الترجمة العربية
                  Text(
                    message['translation'] as String,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade700,
                    ),
                    textAlign: TextAlign.right,
                    textDirection: TextDirection.rtl,
                  ),

                  // أزرار التفاعل (للمتحدث الحالي فقط)
                  if (isCurrentSpeaker && !isCompleted) ...[
                    const SizedBox(height: 8),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // زر الاختبار
                        IconButton(
                          icon: const Icon(Icons.mic, size: 20),
                          onPressed: () => _testSentence(message),
                          tooltip: 'اختبار النطق',
                          color: Colors.blue,
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(),
                        ),
                        const SizedBox(width: 16),
                        // زر تعليم كمقروءة
                        IconButton(
                          icon: const Icon(Icons.check, size: 20),
                          onPressed: () => _markAsCompleted(message),
                          tooltip: 'تم القراءة',
                          color: Colors.green,
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),
          ),

          if (isCurrentSpeaker) ...[
            const SizedBox(width: 8),
            // صورة المتحدث
            Column(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundImage: AssetImage(message['avatar'] as String),
                ),
                const SizedBox(height: 4),
                Text(
                  message['speaker'] as String,
                  style: const TextStyle(fontSize: 12),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  // اختبار نطق الجملة
  void _testSentence(Map<String, dynamic> message) {
    // محاكاة اختبار النطق
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختبار النطق'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(message['text'] as String),
            const SizedBox(height: 16),
            const Text('تم اجتياز الاختبار بنجاح!'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _updatePointsAfterTest(message);
            },
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  // تعليم الجملة كمكتملة
  void _markAsCompleted(Map<String, dynamic> message) async {
    final index = _conversationData.indexOf(message);
    if (index != -1) {
      setState(() {
        _conversationData[index]['isCompleted'] = true;
        _completedSentences++;
        if (_currentSpeakerIndex < _conversationData.length - 1) {
          _currentSpeakerIndex++;
        }
      });

      _updatePointsAfterReading(message);
      _saveConversationForReview();

      // إضافة جملة المحادثة المقروءة لمسار التعلم الجديد
      try {
        final firebaseUser = FirebaseAuth.instance.currentUser;
        if (firebaseUser != null &&
            _groupId != 0 &&
            _currentConversation != null) {
          final msgIndex = message['messageIndex'] ?? index;
          final msgModel =
              (_messages.length > msgIndex) ? _messages[msgIndex] : null;
          if (msgModel != null) {
            final readConversationService = ReadConversationService();
            await readConversationService.addReadConversationSentence(
              userId: firebaseUser.uid,
              conversationGroupId: _groupId,
              sentenceId: msgModel.id,
              order: msgIndex,
            );
            debugPrint(
                'تم حفظ جملة المحادثة في readConversations: groupId=$_groupId, sentenceId=${msgModel.id}, order=$msgIndex');
          }
        }
      } catch (e) {
        debugPrint('خطأ في حفظ جملة المحادثة المقروءة لمسار التعلم: $e');
      }
    }
  }

  // حفظ المحادثة في قائمة الجمل المقروءة للمراجعة
  Future<void> _saveConversationForReview() async {
    if (_currentConversation == null) return;

    try {
      final conversationService =
          Provider.of<ConversationService>(context, listen: false);

      // الحصول على مؤشر الرسالة الحالية
      final messageIndex = _currentSpeakerIndex < _conversationData.length
          ? _conversationData[_currentSpeakerIndex]['messageIndex'] ?? 0
          : 0;

      // تحديث تقدم القراءة مع معلومات المستوى والدورة والمجموعة
      await conversationService.updateReadProgress(
        _currentConversation!.id,
        messageIndex,
        0.85, // استخدام قيمة افتراضية للدقة
        levelId: _levelId,
        cycleId: _cycleId,
        groupId: _groupId,
      );

      debugPrint('تم حفظ المحادثة للمراجعة: ${_currentConversation!.id}');
      debugPrint('المستوى: $_levelId، الدورة: $_cycleId، المجموعة: $_groupId');
      debugPrint('مؤشر الرسالة: $messageIndex');
    } catch (e) {
      debugPrint('خطأ في حفظ المحادثة للمراجعة: $e');
    }
  }

  // تحديث النقاط بعد قراءة الجملة
  Future<void> _updatePointsAfterReading(Map<String, dynamic> message) async {
    final levelProvider = Provider.of<LevelProvider>(context, listen: false);
    final pointsProvider = Provider.of<PointsProvider>(context, listen: false);

    // إضافة 10 نقاط لقراءة الجملة
    await pointsProvider.addPoints(
      10,
      PointType.educational,
      'قراءة جملة في المحادثة - المستوى $_levelId، الدورة $_cycleId، المجموعة $_groupId',
    );

    // عرض تأثير النقاط
    if (mounted) {
      PointsAnimationService().showPointsAnimation(
        context,
        10,
        PointType.educational,
      );
    }

    // حساب ما إذا كانت المجموعة مكتملة
    final isGroupCompleted = _completedSentences >= _totalSentences;

    // تحديث تقدم المجموعة
    await levelProvider.updateLessonGroupProgress(
      _levelId,
      _cycleId,
      _groupId,
      _completedSentences,
      _accuracy,
      isGroupCompleted,
    );

    // تحديث تقدم المستوى
    final level = levelProvider.getLevelById(_levelId);
    if (level != null) {
      final earnedPoints = level.earnedEducationalPoints + 10;
      await levelProvider.updateLevelProgress(
        _levelId,
        _completedSentences,
        earnedPoints,
      );
    }

    // التحقق مما إذا تم إكمال المحادثة
    if (_completedSentences >= _totalSentences) {
      // إضافة 50 نقطة إضافية لإكمال المحادثة
      await pointsProvider.addPoints(
        50,
        PointType.educational,
        'إكمال محادثة في المستوى $_levelId، المجموعة $_groupId',
      );

      // عرض تأثير النقاط الإضافية
      if (mounted) {
        PointsAnimationService().showPointsAnimation(
          context,
          50,
          PointType.educational,
        );
      }

      // عرض رسالة نجاح
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تهانينا! تم إكمال المحادثة بنجاح!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  // تحديث النقاط بعد اختبار النطق
  Future<void> _updatePointsAfterTest(Map<String, dynamic> message) async {
    final levelProvider = Provider.of<LevelProvider>(context, listen: false);
    final pointsProvider = Provider.of<PointsProvider>(context, listen: false);

    // إضافة 5 نقاط لاختبار النطق
    await pointsProvider.addPoints(
      5,
      PointType.educational,
      'اختبار نطق في المحادثة - المستوى $_levelId، الدورة $_cycleId، المجموعة $_groupId',
    );

    // عرض تأثير النقاط
    if (mounted) {
      PointsAnimationService().showPointsAnimation(
        context,
        5,
        PointType.educational,
      );
    }

    // تحديث دقة النطق
    final newAccuracy =
        (_accuracy * _completedSentences + 0.85) / (_completedSentences + 1);
    setState(() {
      _accuracy = newAccuracy;
    });

    // تحديث تقدم المجموعة
    await levelProvider.updateLessonGroupProgress(
      _levelId,
      _cycleId,
      _groupId,
      _completedSentences,
      _accuracy,
      _completedSentences >= _totalSentences,
    );
  }

  // الحصول على لون دقة النطق
  Color _getAccuracyColor() {
    if (_accuracy >= 0.8) {
      return Colors.green;
    } else if (_accuracy >= 0.6) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_title),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadConversation,
            tooltip: 'تحديث المحادثة',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // معلومات التقدم
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: _buildProgressInfo(),
                ),

                // المحادثة
                Expanded(
                  child: _conversationData.isEmpty
                      ? const Center(
                          child: Text('لا توجد محادثة متاحة حاليًا'),
                        )
                      : ListView.builder(
                          padding: const EdgeInsets.all(16),
                          // عرض فقط الرسائل حتى المؤشر الحالي + 1
                          itemCount: _currentSpeakerIndex + 1 >
                                  _conversationData.length
                              ? _conversationData.length
                              : _currentSpeakerIndex + 1,
                          itemBuilder: (context, index) {
                            final message = _conversationData[index];
                            final isCurrentSpeaker =
                                index == _currentSpeakerIndex;
                            return _buildChatBubble(message, isCurrentSpeaker);
                          },
                        ),
                ),
              ],
            ),
    );
  }
}
