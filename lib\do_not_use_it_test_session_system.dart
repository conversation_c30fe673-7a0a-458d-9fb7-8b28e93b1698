import 'package:flutter/material.dart';
import 'services/learning_path_session_service.dart';
import 'services/conversation_session_service.dart';
import 'models/sentence_model.dart';
import 'models/conversation_model.dart';
import 'models/message_model.dart';

/// ملف اختبار نظام الجلسات
/// يمكن استخدامه للتأكد من عمل النظام بشكل صحيح
class SessionSystemTest {
  static Future<void> testBatchSessions() async {
    debugPrint('🧪 بدء اختبار نظام جلسات الدفعات...');
    
    final sessionService = LearningPathSessionService();
    
    // إنشاء جمل تجريبية
    final testSentences = [
      SentenceModel(
        id: 'test_1',
        englishText: 'Hello world',
        arabicText: 'مرحبا بالعالم',
        category: 'test',
        createdAt: DateTime.now(),
        readBy: {},
        isFavorite: false,
      ),
      SentenceModel(
        id: 'test_2',
        englishText: 'Good morning',
        arabicText: 'صباح الخير',
        category: 'test',
        createdAt: DateTime.now(),
        readBy: {},
        isFavorite: false,
      ),
    ];
    
    // اختبار حفظ الجلسة
    await sessionService.saveBatchSession(
      levelId: 1,
      cycleId: 1,
      groupId: 1,
      sentences: testSentences,
      currentPosition: 0,
    );
    debugPrint('✅ تم حفظ جلسة الدفعة بنجاح');
    
    // اختبار استعادة الجلسة
    final retrievedSession = await sessionService.getBatchSession(
      levelId: 1,
      cycleId: 1,
      groupId: 1,
    );
    
    if (retrievedSession != null) {
      final sentences = retrievedSession['sentences'] as List<SentenceModel>;
      debugPrint('✅ تم استعادة ${sentences.length} جملة من الجلسة');
    } else {
      debugPrint('❌ فشل في استعادة الجلسة');
    }
    
    // اختبار إزالة جملة من الجلسة
    await sessionService.removeSentenceFromSession(
      levelId: 1,
      cycleId: 1,
      groupId: 1,
      sentenceId: 'test_1',
    );
    debugPrint('✅ تم إزالة جملة من الجلسة');
    
    // التحقق من الجلسة بعد الإزالة
    final updatedSession = await sessionService.getBatchSession(
      levelId: 1,
      cycleId: 1,
      groupId: 1,
    );
    
    if (updatedSession != null) {
      final sentences = updatedSession['sentences'] as List<SentenceModel>;
      debugPrint('✅ تبقى ${sentences.length} جملة في الجلسة بعد الإزالة');
    }
    
    // اختبار حذف الجلسة
    await sessionService.clearSession(
      levelId: 1,
      cycleId: 1,
      groupId: 1,
    );
    debugPrint('✅ تم حذف الجلسة');
    
    debugPrint('🎉 انتهى اختبار نظام جلسات الدفعات بنجاح!');
  }
  
  static Future<void> testConversationSessions() async {
    debugPrint('🧪 بدء اختبار نظام جلسات المحادثات...');
    
    final sessionService = ConversationSessionService();
    
    // إنشاء محادثة تجريبية
    final testConversation = ConversationModel(
      id: 'conv_test_1',
      title: 'محادثة تجريبية',
      category: 'test',
      createdAt: DateTime.now(),
      createdBy: 'test_user',
      messages: [
        MessageModel(
          id: 'msg_1',
          englishText: 'Hello',
          arabicText: 'مرحبا',
          speaker: 'A',
          createdAt: DateTime.now(),
          readBy: {},
        ),
        MessageModel(
          id: 'msg_2',
          englishText: 'How are you?',
          arabicText: 'كيف حالك؟',
          speaker: 'B',
          createdAt: DateTime.now(),
          readBy: {},
        ),
      ],
      readBy: {},
    );
    
    // اختبار حفظ جلسة المحادثة
    await sessionService.saveConversationSession(
      levelId: 1,
      cycleId: 1,
      groupId: 2,
      conversation: testConversation,
      visibleMessageCount: 1,
    );
    debugPrint('✅ تم حفظ جلسة المحادثة بنجاح');
    
    // اختبار استعادة جلسة المحادثة
    final retrievedSession = await sessionService.getConversationSession(
      levelId: 1,
      cycleId: 1,
      groupId: 2,
    );
    
    if (retrievedSession != null) {
      final conversationId = retrievedSession['conversationId'] as String;
      final messageIds = retrievedSession['displayedMessageIds'] as List<String>;
      debugPrint('✅ تم استعادة جلسة المحادثة: $conversationId مع ${messageIds.length} رسالة');
    } else {
      debugPrint('❌ فشل في استعادة جلسة المحادثة');
    }
    
    // اختبار تحديث عدد الرسائل المعروضة
    await sessionService.updateVisibleMessageCount(
      levelId: 1,
      cycleId: 1,
      groupId: 2,
      newCount: 2,
    );
    debugPrint('✅ تم تحديث عدد الرسائل المعروضة');
    
    // اختبار حذف جلسة المحادثة
    await sessionService.clearConversationSession(
      levelId: 1,
      cycleId: 1,
      groupId: 2,
    );
    debugPrint('✅ تم حذف جلسة المحادثة');
    
    debugPrint('🎉 انتهى اختبار نظام جلسات المحادثات بنجاح!');
  }
  
  static Future<void> runAllTests() async {
    debugPrint('🚀 بدء اختبار نظام الجلسات الكامل...');
    
    try {
      await testBatchSessions();
      await testConversationSessions();
      debugPrint('🎊 تم اجتياز جميع الاختبارات بنجاح!');
    } catch (e) {
      debugPrint('💥 فشل في الاختبارات: $e');
    }
  }
}
