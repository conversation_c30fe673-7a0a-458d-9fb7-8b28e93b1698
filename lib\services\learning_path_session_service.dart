import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/sentence_model.dart';

/// خدمة إدارة جلسات مسار التعلم
/// تحفظ حالة الجمل المعروضة حاليًا في الدفعات والمحادثات
class LearningPathSessionService {
  // مفاتيح التخزين المحلي
  static const String _sessionPrefix = 'learning_path_session_';
  static const String _conversationSessionPrefix = 'conversation_session_';
  static const String _currentPositionPrefix = 'current_position_';

  /// حفظ جلسة دفعة جمل
  Future<void> saveBatchSession({
    required int levelId,
    required int cycleId,
    required int groupId,
    required List<SentenceModel> sentences,
    int currentPosition = 0,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final sessionKey = _getBatchSessionKey(levelId, cycleId, groupId);
      final positionKey = _getPositionKey(levelId, cycleId, groupId);
      
      // حفظ الجمل
      final sentencesJson = sentences.map((s) => s.toJson()).toList();
      await prefs.setString(sessionKey, jsonEncode(sentencesJson));
      
      // حفظ الموقع الحالي
      await prefs.setInt(positionKey, currentPosition);
      
      debugPrint('تم حفظ جلسة الدفعة: المستوى $levelId، الدورة $cycleId، المجموعة $groupId');
      debugPrint('عدد الجمل: ${sentences.length}، الموقع الحالي: $currentPosition');
    } catch (e) {
      debugPrint('خطأ في حفظ جلسة الدفعة: $e');
    }
  }

  /// استعادة جلسة دفعة جمل
  Future<Map<String, dynamic>?> getBatchSession({
    required int levelId,
    required int cycleId,
    required int groupId,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final sessionKey = _getBatchSessionKey(levelId, cycleId, groupId);
      final positionKey = _getPositionKey(levelId, cycleId, groupId);
      
      final sentencesJson = prefs.getString(sessionKey);
      final currentPosition = prefs.getInt(positionKey) ?? 0;
      
      if (sentencesJson == null || sentencesJson.isEmpty) {
        return null;
      }
      
      final List<dynamic> decoded = jsonDecode(sentencesJson);
      final sentences = decoded.map((json) => SentenceModel.fromJson(json)).toList();
      
      debugPrint('تم استعادة جلسة الدفعة: المستوى $levelId، الدورة $cycleId، المجموعة $groupId');
      debugPrint('عدد الجمل: ${sentences.length}، الموقع الحالي: $currentPosition');
      
      return {
        'sentences': sentences,
        'currentPosition': currentPosition,
      };
    } catch (e) {
      debugPrint('خطأ في استعادة جلسة الدفعة: $e');
      return null;
    }
  }

  /// حفظ جلسة محادثة
  Future<void> saveConversationSession({
    required int levelId,
    required int cycleId,
    required int groupId,
    required String conversationId,
    required List<String> displayedMessageIds,
    int currentMessageIndex = 0,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final sessionKey = _getConversationSessionKey(levelId, cycleId, groupId);
      final positionKey = _getPositionKey(levelId, cycleId, groupId);
      
      // حفظ معرفات الرسائل المعروضة
      final sessionData = {
        'conversationId': conversationId,
        'displayedMessageIds': displayedMessageIds,
      };
      await prefs.setString(sessionKey, jsonEncode(sessionData));
      
      // حفظ فهرس الرسالة الحالية
      await prefs.setInt(positionKey, currentMessageIndex);
      
      debugPrint('تم حفظ جلسة المحادثة: المستوى $levelId، الدورة $cycleId، المجموعة $groupId');
      debugPrint('معرف المحادثة: $conversationId، عدد الرسائل: ${displayedMessageIds.length}');
    } catch (e) {
      debugPrint('خطأ في حفظ جلسة المحادثة: $e');
    }
  }

  /// استعادة جلسة محادثة
  Future<Map<String, dynamic>?> getConversationSession({
    required int levelId,
    required int cycleId,
    required int groupId,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final sessionKey = _getConversationSessionKey(levelId, cycleId, groupId);
      final positionKey = _getPositionKey(levelId, cycleId, groupId);
      
      final sessionJson = prefs.getString(sessionKey);
      final currentMessageIndex = prefs.getInt(positionKey) ?? 0;
      
      if (sessionJson == null || sessionJson.isEmpty) {
        return null;
      }
      
      final Map<String, dynamic> sessionData = jsonDecode(sessionJson);
      
      debugPrint('تم استعادة جلسة المحادثة: المستوى $levelId، الدورة $cycleId، المجموعة $groupId');
      debugPrint('معرف المحادثة: ${sessionData['conversationId']}');
      
      return {
        'conversationId': sessionData['conversationId'],
        'displayedMessageIds': List<String>.from(sessionData['displayedMessageIds'] ?? []),
        'currentMessageIndex': currentMessageIndex,
      };
    } catch (e) {
      debugPrint('خطأ في استعادة جلسة المحادثة: $e');
      return null;
    }
  }

  /// تحديث موقع المستخدم في الجلسة
  Future<void> updateSessionPosition({
    required int levelId,
    required int cycleId,
    required int groupId,
    required int newPosition,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final positionKey = _getPositionKey(levelId, cycleId, groupId);
      await prefs.setInt(positionKey, newPosition);
      
      debugPrint('تم تحديث موقع الجلسة: المستوى $levelId، الدورة $cycleId، المجموعة $groupId، الموقع الجديد: $newPosition');
    } catch (e) {
      debugPrint('خطأ في تحديث موقع الجلسة: $e');
    }
  }

  /// حذف جلسة مكتملة
  Future<void> clearSession({
    required int levelId,
    required int cycleId,
    required int groupId,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final batchSessionKey = _getBatchSessionKey(levelId, cycleId, groupId);
      final conversationSessionKey = _getConversationSessionKey(levelId, cycleId, groupId);
      final positionKey = _getPositionKey(levelId, cycleId, groupId);
      
      await prefs.remove(batchSessionKey);
      await prefs.remove(conversationSessionKey);
      await prefs.remove(positionKey);
      
      debugPrint('تم حذف جلسة: المستوى $levelId، الدورة $cycleId، المجموعة $groupId');
    } catch (e) {
      debugPrint('خطأ في حذف الجلسة: $e');
    }
  }

  /// التحقق من وجود جلسة نشطة
  Future<bool> hasActiveSession({
    required int levelId,
    required int cycleId,
    required int groupId,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final batchSessionKey = _getBatchSessionKey(levelId, cycleId, groupId);
      final conversationSessionKey = _getConversationSessionKey(levelId, cycleId, groupId);
      
      return prefs.containsKey(batchSessionKey) || prefs.containsKey(conversationSessionKey);
    } catch (e) {
      debugPrint('خطأ في التحقق من الجلسة النشطة: $e');
      return false;
    }
  }

  /// حذف جميع الجلسات (للتنظيف)
  Future<void> clearAllSessions() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();
      
      for (final key in keys) {
        if (key.startsWith(_sessionPrefix) || 
            key.startsWith(_conversationSessionPrefix) || 
            key.startsWith(_currentPositionPrefix)) {
          await prefs.remove(key);
        }
      }
      
      debugPrint('تم حذف جميع جلسات مسار التعلم');
    } catch (e) {
      debugPrint('خطأ في حذف جميع الجلسات: $e');
    }
  }

  /// الحصول على مفتاح جلسة الدفعة
  String _getBatchSessionKey(int levelId, int cycleId, int groupId) {
    return '${_sessionPrefix}batch_${levelId}_${cycleId}_$groupId';
  }

  /// الحصول على مفتاح جلسة المحادثة
  String _getConversationSessionKey(int levelId, int cycleId, int groupId) {
    return '$_conversationSessionPrefix${levelId}_${cycleId}_$groupId';
  }

  /// الحصول على مفتاح الموقع الحالي
  String _getPositionKey(int levelId, int cycleId, int groupId) {
    return '$_currentPositionPrefix${levelId}_${cycleId}_$groupId';
  }

  /// إزالة الجمل المقروءة من الجلسة
  Future<void> removeSentenceFromSession({
    required int levelId,
    required int cycleId,
    required int groupId,
    required String sentenceId,
  }) async {
    try {
      final session = await getBatchSession(
        levelId: levelId,
        cycleId: cycleId,
        groupId: groupId,
      );
      
      if (session != null) {
        final sentences = session['sentences'] as List<SentenceModel>;
        final updatedSentences = sentences.where((s) => s.id != sentenceId).toList();
        
        if (updatedSentences.isEmpty) {
          // إذا لم تعد هناك جمل، احذف الجلسة
          await clearSession(levelId: levelId, cycleId: cycleId, groupId: groupId);
        } else {
          // احفظ الجمل المحدثة
          await saveBatchSession(
            levelId: levelId,
            cycleId: cycleId,
            groupId: groupId,
            sentences: updatedSentences,
            currentPosition: session['currentPosition'] as int,
          );
        }
      }
    } catch (e) {
      debugPrint('خطأ في إزالة الجملة من الجلسة: $e');
    }
  }
}
